{"appid": "wx45960edf3707de51", "compileType": "miniprogram", "libVersion": "3.7.1", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useMultiFrameRuntime": true, "packNpmManually": true, "minifyWXSS": true, "useStaticServer": true, "showES6CompileOption": false, "checkInvalidKey": true, "disableUseStrict": false, "useCompilerPlugins": false, "minifyWXML": true, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "simulatorPluginLibVersion": {}}